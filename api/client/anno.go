package client

import (
	"bytes"
	"compress/gzip"
	"context"
	"fmt"
	"io"
	"os"
	"strings"

	"annout/internal/conf"

	"github.com/go-kratos/kratos/v2/encoding"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/client"
	"gitlab.rp.konvery.work/platform/pkg/download"
)

type (
	Job     = anno.Job
	JobAnno = anno.JobAnno
	Anno    = client.Anno
)

func initAnno(c *conf.Bootstrap) {
	client.InitAnno(&client.Service{Addr: c.Rpc.Anno.Addr})
}

var (
	SetAnnoSvc         = client.SetAnnoSvc
	GetLot             = client.GetLot
	ListJob            = client.ListJob
	GetOrder           = client.GetOrder
	SetOrderAnnoResult = client.SetOrderAnnoResult
	SetLotAnnoResult   = client.SetLotAnnoResult

	JobAnnoFromJob = client.JobAnnoFromJob
)

func JSONCodec() encoding.Codec { return encoding.GetCodec("json") }

// DownloadJobDetails downloads job elements info and annos
func DownloadJobDetails(ctx context.Context, jobs ...*client.Job) (err error) {
	for i, job := range jobs {
		if (len(job.Elements) == 0 && len(job.ElementsUrls) == 0) ||
			(len(job.Annotations) == 0 && job.AnnotationsUrl == "") {
			// work around old jobs whose elements info is saved in database directly
			job, err = client.GetJob(ctx, job.Uid, false)
			if err != nil {
				return fmt.Errorf("failed to get job: %w", err)
			}
			jobs[i] = job
		}

		for _, url := range job.ElementsUrls {
			data, err := DownloadData[anno.Job_ElementData](ctx, url)
			if err != nil {
				return fmt.Errorf("failed to download job elements info: %w", err)
			}
			job.Elements = append(job.Elements, data.GetElements()...)
			job.CamParams = data.GetCamParams()
		}
		if job.AnnotationsUrl != "" {
			data, err := DownloadData[anno.Job_AnnotationData](ctx, job.AnnotationsUrl)
			if err != nil {
				return fmt.Errorf("failed to download job annos: %w", err)
			}
			job.Annotations = data.GetElementAnnos()
		}
	}
	return nil
}

func DownloadData[T any](ctx context.Context, uri string) (data *T, err error) {
	fpath, err := download.Download(ctx, &download.File{URI: uri})
	if err != nil {
		return nil, fmt.Errorf("failed to download file: %w", err)
	}
	defer os.RemoveAll(fpath)

	fdata, err := os.ReadFile(fpath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}
	// 判断是否gzip数据 解压缩
	if strings.HasSuffix(uri, ".gz") || isGzipData(fdata) {
		fmt.Println("gzip file detected")
		fdata, err = DecompressGzipData(fdata)
		if err != nil {
			return nil, fmt.Errorf("failed to decompress file: %w", err)
		}
		fmt.Println("gzip decompression successful")
	}
	if err := JSONCodec().Unmarshal(fdata, &data); err != nil {
		return nil, fmt.Errorf("failed to unmarshal data: %w", err)
	}
	return data, nil
}

func isGzipData(data []byte) bool {
	return len(data) > 2 && data[0] == 0x1F && data[1] == 0x8B
}

// DecompressGzipData decompresses the input GZIP data and returns the decompressed byte slice.
func DecompressGzipData(data []byte) ([]byte, error) {
	zr, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer zr.Close()
	// Log the internal file name (if available).
	fmt.Println("Decompressed file name:", zr.Name)
	decompressedData, err := io.ReadAll(zr)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress gzip data: %w", err)
	}

	return decompressedData, nil
}

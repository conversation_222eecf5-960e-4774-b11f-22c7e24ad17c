package client

import (
	"annout/internal/conf"
	"gitlab.rp.konvery.work/platform/apis/annofeed/v1"
	"gitlab.rp.konvery.work/platform/apis/client"
)

type (
	CreateFileRequest = annofeed.CreateFileRequest
	Annofeed          = client.Annofeed
)

func initAnnofeed(c *conf.Bootstrap) {
	client.InitAnnofeed(&client.Service{Addr: c.Rpc.Annofeed.Addr})
}

var (
	SetAnnofeedSvc   = client.SetAnnofeedSvc
	CreateFile       = client.CreateFile
	FinishFileUpload = client.FinishFileUpload
	ShareFile        = client.ShareFile
	GetDataMeta      = client.GetDataMeta
)

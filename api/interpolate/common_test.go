package interpolate

import (
	"fmt"
	"math"
	"testing"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"

	"github.com/stretchr/testify/assert"
)

func TestBox2dInterpolate(t *testing.T) {
	o1 := &anno.Object{
		Uuid:    "1111",
		TrackId: "car-1",
		Label: &anno.Object_Label{
			Name:  "car",
			Attrs: []*anno.AttrAndValues{},
			Widget: &anno.Object_Widget{
				Name: anno.WidgetName_box2d,
				Data: []float64{200, 200, 100, 100, 100},
				Forward: &anno.Direction{
					Origin: []float64{200, 200},
					Toward: []float64{200, 300},
				},
			},
		},
	}
	o2 := cloneObject(o1)
	o2.Label.Widget.Data = []float64{400, 400, 200, 200, 100}
	o2.Label.Widget.Forward = &anno.Direction{
		Origin: []float64{400, 400},
		Toward: []float64{400, 500},
	}

	cases := []struct {
		o1         *anno.Object
		o2         *anno.Object
		span       float64
		distance   float64
		radianDiff float64
		exp        []float64
		expForward *anno.Direction
	}{
		{
			o1, nil, 10, 0, 0,
			o1.Label.Widget.Data,
			o1.Label.Widget.Forward,
		},
		{
			o1, nil, 10, 5, 0,
			o1.Label.Widget.Data,
			o1.Label.Widget.Forward,
		},
		{
			o1, o1, 5, 1, 0,
			o1.Label.Widget.Data,
			o1.Label.Widget.Forward,
		},
		{
			o1, o1, 5, 3, 0,
			o1.Label.Widget.Data,
			o1.Label.Widget.Forward,
		},
		{
			o1, o1, 5, 5, 0,
			o1.Label.Widget.Data,
			o1.Label.Widget.Forward,
		},
		{
			o1, o2, 1, 0, 0,
			o1.Label.Widget.Data,
			o1.Label.Widget.Forward,
		},
		{
			o1, o2, 1, 1, 0,
			o2.Label.Widget.Data,
			o2.Label.Widget.Forward,
		},
		{
			o1, o2, 10, 0, 0,
			o1.Label.Widget.Data,
			o1.Label.Widget.Forward,
		},
		{
			o1, o2, 10, 1, math.Pi / 2,
			[]float64{220, 220, 110, 110, 100.15707963267948},
			/*
				forward: {origin=[200, 200], toward=[200, 300]}
				center: [200, 200]
				radianDiff: math.Pi / 2 * 1 / 10
				toward rotates around origin by radianDiff and becomes [215.64344650402307, 298.7688340595138]
				displacement: [20, 20]
				After adding displacement, newForward: {origin=[220, 220], toward=[235.64344650402307, 318.7688340595138]}
			*/
			&anno.Direction{
				Origin: []float64{220, 220},
				Toward: []float64{235.64344650402248, 318.7688340595139},
			},
		},
		{
			o1, o2, 10, 2, math.Pi / 2,
			[]float64{240, 240, 120, 120, 100.31415926535898},
			/*
				forward: {origin=[200, 200], toward=[200, 300]}
				center: [200, 200]
				radianDiff: math.Pi / 2 * 2 / 10
				toward rotates around origin by radianDiff and becomes [230.9016994374949, 295.1056516295153]
				displacement: [40, 40]
				After adding displacement, newForward: {origin=[240, 240], toward=[270.9016994374949, 335.1056516295153]}
			*/
			&anno.Direction{
				Origin: []float64{240, 240},
				Toward: []float64{270.9016994374949, 335.1056516295153},
			},
		},
		{
			o1, o2, 10, 5, math.Pi / 2,
			[]float64{300, 300, 150, 150, 100.78539816339745},
			&anno.Direction{
				Origin: []float64{300, 300},
				Toward: []float64{370.7106781186546, 370.7106781186549},
			},
		},
		{
			o1, o2, 10, 9, 0,
			[]float64{380, 380, 190, 190, 100},
			&anno.Direction{
				Origin: []float64{380, 380},
				Toward: []float64{380, 480},
			},
		},
		{
			o1, o2, 10, 10, 0,
			o2.Label.Widget.Data,
			o2.Label.Widget.Forward,
		},
	}

	for i, c := range cases {
		t.Run(fmt.Sprintf("box2d_case_%d", i), func(t *testing.T) {
			size := len(c.o1.Label.Widget.Data)
			var originalAlpha float64
			if c.o2 != nil {
				originalAlpha = c.o2.Label.Widget.Data[size-1]
				c.o2.Label.Widget.Data[size-1] += c.radianDiff
			}

			o := commonInterpolate(c.o1, c.o2, c.span, c.distance)
			assert.EqualValues(t, c.exp, o.Label.Widget.Data)
			assert.EqualValues(t, c.expForward.Origin, o.Label.Widget.Forward.Origin)
			assert.EqualValues(t, c.expForward.Toward, o.Label.Widget.Forward.Toward)

			if c.o2 != nil {
				c.o2.Label.Widget.Data[size-1] = originalAlpha
			}
		})
	}
}

func Test_commonInterpolate(t *testing.T) {
	o1 := &anno.Object{
		Label: &anno.Object_Label{
			Name:   "car",
			Widget: &anno.Object_Widget{},
		},
	}
	o2 := cloneObject(o1)

	cases := []struct {
		name     anno.WidgetName_Enum
		data1    []float64
		data2    []float64
		span     float64
		distance float64
		exp      []float64
	}{
		{
			anno.WidgetName_point2d,
			[]float64{200, 200},
			[]float64{400, 300},
			10,
			1,
			[]float64{220, 210},
		},
		{
			anno.WidgetName_point3d,
			[]float64{200, 200, 300},
			[]float64{400, 300, 500},
			10,
			1,
			[]float64{220, 210, 320},
		},
		{
			anno.WidgetName_line2d,
			[]float64{200, 200, 300, 300},
			[]float64{400, 300, 500, 500},
			10,
			1,
			[]float64{220, 210, 320, 320},
		},
		{
			anno.WidgetName_line3d,
			[]float64{200, 200, 300, 400, 400, 400},
			[]float64{400, 300, 500, 600, 600, 600},
			10,
			1,
			[]float64{220, 210, 320, 420, 420, 420},
		},
		{
			anno.WidgetName_poly2d,
			[]float64{0, 0, 0, 300, 300, 300, 300, 0},
			[]float64{100, 100, 100, 200, 200, 200, 200, 100},
			10,
			1,
			[]float64{10, 10, 10, 290, 290, 290, 290, 10},
		},
		{
			anno.WidgetName_poly3d,
			[]float64{ // 正方体，边长300
				0, 0, 0,
				0, 0, 300,
				0, 300, 300,
				0, 300, 0,
				300, 0, 0,
				300, 0, 300,
				300, 300, 300,
				300, 300, 0,
			},
			[]float64{ // 正方体，边长100
				100, 100, 100,
				100, 100, 200,
				100, 200, 200,
				100, 200, 100,
				200, 100, 100,
				200, 100, 200,
				200, 200, 200,
				200, 200, 100,
			},
			10,
			1,
			[]float64{
				10, 10, 10,
				10, 10, 290,
				10, 290, 290,
				10, 290, 10,
				290, 10, 10,
				290, 10, 290,
				290, 290, 290,
				290, 290, 10,
			},
		},
		{
			anno.WidgetName_pscuboid,
			[]float64{200, 200, 100, 100, 300, 300, 100, 100, 100},
			[]float64{400, 400, 200, 200, 400, 400, 100, 100, 100},
			10,
			1,
			[]float64{220, 220, 110, 110, 310, 310, 100, 100, 100},
		},
	}

	for _, c := range cases {
		t.Run(c.name.String(), func(t *testing.T) {
			o1.Label.Widget.Name = c.name
			o1.Label.Widget.Data = c.data1
			o2.Label.Widget.Data = c.data2

			o := commonInterpolate(o1, o2, c.span, c.distance)
			assert.EqualValues(t, c.exp, o.Label.Widget.Data)
		})
	}
}

func Test_rotateAroundCenter(t *testing.T) {
	cases := []struct {
		name   string
		src    Point
		center Point
		radian float64
		exp    Point
	}{
		{
			"center_zero_rotates_45_degrees_clockwise",
			Point{0, 2},
			Point{0, 0},
			math.Pi / 4,
			Point{1.414213562373095, 1.4142135623730951}, // math.Sqrt(2), math.Sqrt(2)
		},
		{
			"center_zero_rotates_90_degrees_clockwise",
			Point{0, 2},
			Point{0, 0},
			math.Pi / 2,
			Point{2, 1.2246467991473515e-16}, // 2, 0
		},
		{
			"center_non_zero_rotates_45_degrees_clockwise",
			Point{0, 2},
			Point{1, 1},
			math.Pi / 4,
			Point{0.9999999999999999, 2.414213562373095}, // 1, 1 + math.Sqrt(2)
		},
		{
			"center_non_zero_rotates_90_degrees_clockwise",
			Point{0, 2},
			Point{1, 1},
			math.Pi / 2,
			Point{2, 2},
		},
		{
			"happy_flow_for_TestBox2dInterpolate",
			Point{200, 300},
			Point{200, 200},
			math.Pi / 20,
			Point{215.64344650402307, 298.7688340595138},
		},
		{
			"happy_flow_for_TestBox2dInterpolate",
			Point{200, 300},
			Point{200, 200},
			math.Pi / 10,
			Point{230.90169943749473, 295.10565162951536},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			got := rotateAroundCenter(c.src, c.center, c.radian)
			assert.Equal(t, c.exp, got)
		})
	}
}

#!/usr/bin/env python3
"""
测试简化版数据导出工具（不连接真实数据库）
"""

from simple_reaper import SimpleConfig, DatabaseConfig

def test_config():
    """测试配置类"""
    print("=== 测试配置类 ===")
    
    # 测试单个 lot_id
    config1 = SimpleConfig(lot_ids="abcdefg")
    print(f"单个 lot_id: {config1.lot_ids}")
    
    # 测试多个 lot_ids
    config2 = SimpleConfig(lot_ids=["abcdefg", "hijklmn"])
    print(f"多个 lot_ids: {config2.lot_ids}")
    
    # 测试数据库配置
    db_config = DatabaseConfig(host="localhost", port=5432, user="test", password="test", database="test_db")
    config3 = SimpleConfig(lot_ids=["test"], db_config=db_config)
    print(f"数据库配置: {config3.db_config.host}:{config3.db_config.port}")
    
    print("✅ 配置类测试通过\n")


def test_uid_conversion():
    """测试 UID 转换函数"""
    print("=== 测试 UID 转换 ===")
    
    from simple_reaper import kw_parse_uid, kw_to_uid
    
    # 测试有效的 UID
    test_uids = ["abcdefg", "hijklmn", "opqrstu", "a", "ab", "abc"]
    
    for uid in test_uids:
        try:
            # UID -> 数字
            num = kw_parse_uid(uid)
            # 数字 -> UID
            converted_uid = kw_to_uid(num)
            print(f"UID: {uid:10} -> 数字: {num:10} -> UID: {converted_uid}")
            
            # 验证转换是否正确
            if uid == converted_uid:
                print(f"  ✅ 转换正确")
            else:
                print(f"  ❌ 转换错误: {uid} != {converted_uid}")
        except Exception as e:
            print(f"  ❌ 转换失败: {e}")
    
    print("✅ UID 转换测试完成\n")


def test_invalid_uid():
    """测试无效的 UID"""
    print("=== 测试无效 UID ===")
    
    from simple_reaper import kw_parse_uid
    
    invalid_uids = ["lot-12345", "test_123", "ABC", "123", "test-uid"]
    
    for uid in invalid_uids:
        try:
            num = kw_parse_uid(uid)
            print(f"  ❌ 应该失败但成功了: {uid} -> {num}")
        except ValueError as e:
            print(f"  ✅ 正确拒绝无效 UID: {uid} - {e}")
    
    print("✅ 无效 UID 测试完成\n")


def test_config_loading():
    """测试配置文件加载"""
    print("=== 测试配置文件加载 ===")
    
    import json
    import tempfile
    import os
    
    # 创建测试配置
    test_config = {
        "lot_ids": ["abcdefg", "hijklmn"],
        "phases": [1, 2, 3],
        "finished_only": True,
        "output_dir": "./test_exports",
        "database": {
            "host": "test_host",
            "port": 5433,
            "user": "test_user",
            "password": "test_pass",
            "database": "test_db"
        }
    }
    
    # 写入临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(test_config, f)
        temp_file = f.name
    
    try:
        # 加载配置
        from run_export import load_config_from_file
        config = load_config_from_file(temp_file)
        
        print(f"加载的配置:")
        print(f"  lot_ids: {config.lot_ids}")
        print(f"  phases: {config.phases}")
        print(f"  finished_only: {config.finished_only}")
        print(f"  数据库: {config.db_config.host}:{config.db_config.port}")
        
        print("✅ 配置文件加载测试通过")
        
    finally:
        # 清理临时文件
        os.unlink(temp_file)
    
    print()


def main():
    """运行所有测试"""
    print("简化版数据导出工具测试")
    print("=" * 50)
    
    try:
        test_config()
        test_uid_conversion()
        test_invalid_uid()
        test_config_loading()
        
        print("🎉 所有测试通过！")
        print("\n📝 注意事项:")
        print("- 这些测试不需要连接真实数据库")
        print("- 要测试完整功能，需要配置 PostgreSQL 数据库")
        print("- 确保数据库中有对应的 lots 和 jobs 表")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

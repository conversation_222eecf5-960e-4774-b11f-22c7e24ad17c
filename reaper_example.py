#!/usr/bin/env python3
"""
Example usage of the Reaper Workflow
This demonstrates how to use the Python implementation
"""

import json
import logging
from reaper_workflow import ReaperWorkflow, ReaperConfig, ExportOption, MockAPIClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def example_basic_workflow():
    """Basic workflow example without conversion"""
    print("=== Basic Workflow Example ===")
    
    # Initialize API client
    api_client = MockAPIClient("http://localhost:8080")
    
    # Create workflow instance
    workflow = ReaperWorkflow(api_client)
    
    # Basic configuration
    config = ReaperConfig(
        lot_uid="lot-basic-001",
        order_uid="order-basic-001",
        data_uid="data-basic-001",
        encoder="json",
        style="raw",
        option=ExportOption.ALL,
        phases=[1, 2, 3]
    )
    
    # Run workflow
    result = workflow.run(config)
    print(f"Basic workflow result: {result}")
    print()


def example_workflow_with_conversion():
    """Workflow example with annotation conversion"""
    print("=== Workflow with Conversion Example ===")
    
    # Initialize API client
    api_client = MockAPIClient("http://localhost:8080")
    
    # Create workflow instance
    workflow = ReaperWorkflow(api_client)
    
    # Configuration with converter
    config = ReaperConfig(
        lot_uid="lot-convert-001",
        order_uid="order-convert-001",
        data_uid="data-convert-001",
        encoder="json",
        style="raw",
        option=ExportOption.FINISHED,
        phases=[1, 2],
        converter_uri="https://example.com/custom-converter.py"
    )
    
    # Run workflow
    result = workflow.run(config)
    print(f"Conversion workflow result: {result}")
    print()


def example_workflow_with_export():
    """Workflow example with external export"""
    print("=== Workflow with Export Example ===")
    
    # Initialize API client
    api_client = MockAPIClient("http://localhost:8080")
    
    # Create workflow instance
    workflow = ReaperWorkflow(api_client)
    
    # Configuration with exporter
    config = ReaperConfig(
        lot_uid="lot-export-001",
        order_uid="order-export-001",
        data_uid="data-export-001",
        encoder="json",
        style="raw",
        option=ExportOption.FINISHED,
        phases=[1, 2, 3],
        converter_uri="https://example.com/converter.py",
        exporter_name="http",
        exporter_config={
            "upload_url": "https://external-system.com/api/upload",
            "headers": {
                "Authorization": "Bearer your-token-here",
                "Content-Type": "application/zip"
            }
        }
    )
    
    # Run workflow
    result = workflow.run(config)
    print(f"Export workflow result: {result}")
    print()


def example_from_config_file():
    """Load configuration from JSON file"""
    print("=== Configuration from File Example ===")
    
    # Example configuration
    config_data = {
        "lot_uid": "lot-config-001",
        "order_uid": "order-config-001",
        "data_uid": "data-config-001",
        "encoder": "json",
        "style": "raw",
        "option": "finished",
        "phases": [1, 2, 3],
        "converter_uri": "https://example.com/converter.py",
        "exporter_name": "http",
        "exporter_config": {
            "upload_url": "https://external-system.com/upload",
            "headers": {
                "Authorization": "Bearer token123"
            }
        }
    }
    
    # Save to file
    with open("reaper_config.json", "w") as f:
        json.dump(config_data, f, indent=2)
    
    # Load from file
    with open("reaper_config.json", "r") as f:
        loaded_config = json.load(f)
    
    # Convert to ReaperConfig
    config = ReaperConfig(
        lot_uid=loaded_config["lot_uid"],
        order_uid=loaded_config["order_uid"],
        data_uid=loaded_config["data_uid"],
        encoder=loaded_config["encoder"],
        style=loaded_config["style"],
        option=ExportOption(loaded_config["option"]),
        phases=loaded_config["phases"],
        converter_uri=loaded_config["converter_uri"],
        exporter_name=loaded_config["exporter_name"],
        exporter_config=loaded_config["exporter_config"]
    )
    
    # Initialize API client and workflow
    api_client = MockAPIClient("http://localhost:8080")
    workflow = ReaperWorkflow(api_client)
    
    # Run workflow
    result = workflow.run(config)
    print(f"Config file workflow result: {result}")
    print()


def example_batch_processing():
    """Process multiple lots in batch"""
    print("=== Batch Processing Example ===")
    
    # Initialize API client and workflow
    api_client = MockAPIClient("http://localhost:8080")
    workflow = ReaperWorkflow(api_client)
    
    # List of lots to process
    lots = [
        {"lot_uid": "lot-batch-001", "order_uid": "order-batch-001"},
        {"lot_uid": "lot-batch-002", "order_uid": "order-batch-002"},
        {"lot_uid": "lot-batch-003", "order_uid": "order-batch-003"},
    ]
    
    results = []
    
    for lot_info in lots:
        config = ReaperConfig(
            lot_uid=lot_info["lot_uid"],
            order_uid=lot_info["order_uid"],
            data_uid=f"data-{lot_info['lot_uid']}",
            encoder="json",
            style="raw",
            option=ExportOption.ALL,
            phases=[1, 2, 3]
        )
        
        try:
            result = workflow.run(config)
            results.append({
                "lot_uid": lot_info["lot_uid"],
                "status": result,
                "success": result == "finished"
            })
            print(f"Processed {lot_info['lot_uid']}: {result}")
        except Exception as e:
            results.append({
                "lot_uid": lot_info["lot_uid"],
                "status": "failed",
                "success": False,
                "error": str(e)
            })
            print(f"Failed to process {lot_info['lot_uid']}: {e}")
    
    # Summary
    successful = sum(1 for r in results if r["success"])
    total = len(results)
    print(f"Batch processing completed: {successful}/{total} successful")
    print()


def main():
    """Run all examples"""
    print("Reaper Workflow Python Implementation Examples")
    print("=" * 50)
    
    try:
        example_basic_workflow()
        example_workflow_with_conversion()
        example_workflow_with_export()
        example_from_config_file()
        example_batch_processing()
        
        print("All examples completed successfully!")
        
    except Exception as e:
        logger.error(f"Example failed: {e}")
        raise


if __name__ == "__main__":
    main()

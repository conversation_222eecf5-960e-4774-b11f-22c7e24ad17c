# 简化版数据导出工具

这是一个最简化的数据导出工具，只实现核心功能：**收集数据 -> 打包保存到本地**

## 功能特点

- ✅ 收集指定批次的注释数据
- ✅ 支持按阶段过滤
- ✅ 保存为 JSON 和 ZIP 格式
- ✅ 本地文件存储
- ✅ 简单易用的命令行界面

## 文件说明

- `simple_reaper.py` - 主要实现文件
- `run_export.py` - 命令行运行脚本
- `simple_config.json` - 配置文件示例
- `README_simple.md` - 使用说明

## 使用方法

### 方法1: 使用配置文件

1. 编辑配置文件 `simple_config.json`:
```json
{
  "lot_id": "your-lot-id",
  "phases": [1, 2, 3],
  "output_dir": "./exports"
}
```

2. 运行导出:
```bash
python3 run_export.py --config simple_config.json
```

### 方法2: 使用命令行参数

```bash
# 导出指定批次的所有阶段
python3 run_export.py --lot-id lot-12345

# 导出指定批次的特定阶段
python3 run_export.py --lot-id lot-12345 --phases 1 2

# 指定输出目录
python3 run_export.py --lot-id lot-12345 --phases 1 2 --output-dir ./my_exports
```

### 方法3: 直接运行主文件

```bash
python3 simple_reaper.py
```

## 输出文件

工具会生成一个 ZIP 文件，包含：

```
lot_12345_phases_1_2_20240101_120000.zip
├── metadata.json              # 元数据信息
├── annotations_page_001.json  # 注释数据（分页）
├── annotations_page_002.json
└── ...
```

### 文件内容说明

**metadata.json**:
```json
{
  "lot_id": "lot-12345",
  "phases": [1, 2],
  "total_annotations": 150,
  "export_time": "2024-01-01T12:00:00",
  "format_version": "1.0"
}
```

**annotations_page_xxx.json**:
```json
[
  {
    "name": "element-1-0",
    "index": 0,
    "rawdata_annos": [...],
    "attrs": [...]
  }
]
```

## API 接口占位符

当前使用模拟数据，需要替换以下方法为真实的 API 调用：

### 1. 获取批次信息
```python
def get_lot_info(self, lot_id: str) -> Dict[str, Any]:
    # TODO: 替换为真实的 curl 调用
    # curl -X GET "http://api.example.com/lots/{lot_id}"
```

### 2. 获取作业列表
```python
def list_jobs(self, lot_id: str, page: int = 0, page_size: int = 10, phases: List[int] = None) -> Dict[str, Any]:
    # TODO: 替换为真实的 curl 调用
    # curl -X GET "http://api.example.com/jobs?lot_id={lot_id}&page={page}&phases={phases}"
```

## 配置参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| lot_id | string | ✅ | 批次ID |
| phases | array | ❌ | 处理阶段列表，空则处理所有阶段 |
| output_dir | string | ❌ | 输出目录，默认 "./exports" |

## 示例运行

```bash
$ python3 run_export.py --lot-id lot-12345 --phases 1 2

📋 配置信息:
   批次ID: lot-12345
   处理阶段: [1, 2]
   输出目录: ./exports

2024-01-01 12:00:00,000 - INFO - 初始化 API 客户端
2024-01-01 12:00:00,001 - INFO - 开始处理批次: lot-12345
2024-01-01 12:00:00,002 - INFO - 开始收集注释数据...
2024-01-01 12:00:00,003 - INFO - 获取作业列表: lot_id=lot-12345, page=0, phases=[1, 2]
2024-01-01 12:00:00,004 - INFO - 已处理第 1 页，当前收集到 30 个注释
2024-01-01 12:00:00,005 - INFO - 获取作业列表: lot_id=lot-12345, page=1, phases=[1, 2]
2024-01-01 12:00:00,006 - INFO - 已处理第 2 页，当前收集到 60 个注释
2024-01-01 12:00:00,007 - INFO - 注释收集完成，总计: 60 个
2024-01-01 12:00:00,008 - INFO - 开始保存数据到本地...
2024-01-01 12:00:00,010 - INFO - 数据已保存到: ./exports/lot_lot-12345_phases_1_2_20240101_120000.zip
2024-01-01 12:00:00,011 - INFO - 处理完成，输出文件: ./exports/lot_lot-12345_phases_1_2_20240101_120000.zip

✅ 导出成功!
📁 输出文件: ./exports/lot_lot-12345_phases_1_2_20240101_120000.zip
```

## 下一步

1. 提供真实的 API curl 命令
2. 替换 `APIClient` 中的占位符方法
3. 根据实际 API 响应格式调整数据处理逻辑

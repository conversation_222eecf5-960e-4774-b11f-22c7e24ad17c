#!/usr/bin/env python3
"""
Simple test script for the Reaper Workflow
"""

import unittest
import tempfile
import os
import json
from unittest.mock import patch, MagicMock
from reaper_workflow import ReaperWorkflow, ReaperConfig, ExportOption, MockAPIClient


class TestReaperWorkflow(unittest.TestCase):
    """Test cases for ReaperWorkflow"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.api_client = MockAPIClient()
        self.workflow = ReaperWorkflow(self.api_client)
        self.config = ReaperConfig(
            lot_uid="test-lot-001",
            order_uid="test-order-001",
            data_uid="test-data-001",
            encoder="json",
            style="raw",
            option=ExportOption.ALL,
            phases=[1, 2]
        )
    
    def test_basic_workflow(self):
        """Test basic workflow without conversion"""
        result = self.workflow.run(self.config)
        self.assertEqual(result, "finished")
    
    def test_workflow_with_conversion(self):
        """Test workflow with conversion"""
        self.config.converter_uri = "https://example.com/converter.py"
        result = self.workflow.run(self.config)
        self.assertEqual(result, "finished")
    
    def test_workflow_with_export(self):
        """Test workflow with export"""
        self.config.converter_uri = "https://example.com/converter.py"
        self.config.exporter_name = "http"
        self.config.exporter_config = {
            "upload_url": "https://example.com/upload",
            "headers": {"Authorization": "Bearer test"}
        }
        result = self.workflow.run(self.config)
        self.assertEqual(result, "finished")
    
    def test_config_creation(self):
        """Test ReaperConfig creation"""
        config = ReaperConfig(
            lot_uid="test-lot",
            order_uid="test-order"
        )
        self.assertEqual(config.lot_uid, "test-lot")
        self.assertEqual(config.order_uid, "test-order")
        self.assertEqual(config.encoder, "json")
        self.assertEqual(config.style, "raw")
        self.assertEqual(config.option, ExportOption.ALL)
        self.assertEqual(config.phases, [])
    
    def test_api_client_methods(self):
        """Test MockAPIClient methods"""
        # Test get_lot
        lot = self.api_client.get_lot("test-lot")
        self.assertIn("uid", lot)
        self.assertEqual(lot["uid"], "test-lot")
        
        # Test list_jobs
        jobs = self.api_client.list_jobs("test-lot", page=0)
        self.assertIn("jobs", jobs)
        self.assertIsInstance(jobs["jobs"], list)
        
        # Test create_file
        file_info = self.api_client.create_file("test.zip", "test-org")
        self.assertIn("file", file_info)
        self.assertIn("upload_urls", file_info)
    
    def test_export_option_enum(self):
        """Test ExportOption enum"""
        self.assertEqual(ExportOption.ALL.value, "all")
        self.assertEqual(ExportOption.FINISHED.value, "finished")
    
    @patch('subprocess.run')
    def test_converter_execution(self, mock_run):
        """Test converter script execution"""
        mock_run.return_value = MagicMock(returncode=0, stderr="")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            input_dir = os.path.join(temp_dir, "input")
            output_dir = os.path.join(temp_dir, "output")
            os.makedirs(input_dir)
            os.makedirs(output_dir)
            
            config = ReaperConfig(
                lot_uid="test-lot",
                converter_uri="https://example.com/converter.py"
            )
            
            # This should not raise an exception
            self.workflow._run_converter(input_dir, output_dir, config)
            mock_run.assert_called_once()


class TestConfigurationLoading(unittest.TestCase):
    """Test configuration loading from files"""
    
    def test_json_config_loading(self):
        """Test loading configuration from JSON"""
        config_data = {
            "lot_uid": "json-test-lot",
            "order_uid": "json-test-order",
            "data_uid": "json-test-data",
            "encoder": "json",
            "style": "raw",
            "option": "finished",
            "phases": [1, 2, 3],
            "converter_uri": "https://example.com/converter.py",
            "exporter_name": "http",
            "exporter_config": {
                "upload_url": "https://example.com/upload"
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            config_file = f.name
        
        try:
            # Load configuration
            with open(config_file, 'r') as f:
                loaded_data = json.load(f)
            
            config = ReaperConfig(
                lot_uid=loaded_data["lot_uid"],
                order_uid=loaded_data["order_uid"],
                data_uid=loaded_data["data_uid"],
                encoder=loaded_data["encoder"],
                style=loaded_data["style"],
                option=ExportOption(loaded_data["option"]),
                phases=loaded_data["phases"],
                converter_uri=loaded_data["converter_uri"],
                exporter_name=loaded_data["exporter_name"],
                exporter_config=loaded_data["exporter_config"]
            )
            
            self.assertEqual(config.lot_uid, "json-test-lot")
            self.assertEqual(config.option, ExportOption.FINISHED)
            self.assertEqual(config.phases, [1, 2, 3])
            self.assertEqual(config.exporter_name, "http")
            
        finally:
            os.unlink(config_file)


class TestErrorHandling(unittest.TestCase):
    """Test error handling scenarios"""
    
    def setUp(self):
        self.api_client = MockAPIClient()
        self.workflow = ReaperWorkflow(self.api_client)
    
    def test_invalid_exporter(self):
        """Test handling of invalid exporter"""
        config = ReaperConfig(
            lot_uid="test-lot",
            converter_uri="https://example.com/converter.py",
            exporter_name="invalid_exporter"
        )
        
        # Should complete but log warning about unknown exporter
        result = self.workflow.run(config)
        self.assertEqual(result, "finished")
    
    @patch('subprocess.run')
    def test_converter_failure(self, mock_run):
        """Test handling of converter script failure"""
        mock_run.return_value = MagicMock(returncode=1, stderr="Converter failed")
        
        config = ReaperConfig(
            lot_uid="test-lot",
            converter_uri="https://example.com/converter.py"
        )
        
        result = self.workflow.run(config)
        self.assertEqual(result, "failed")


def run_integration_test():
    """Run a simple integration test"""
    print("Running integration test...")
    
    api_client = MockAPIClient()
    workflow = ReaperWorkflow(api_client)
    
    # Test different configurations
    configs = [
        ReaperConfig(lot_uid="integration-test-1"),
        ReaperConfig(
            lot_uid="integration-test-2",
            converter_uri="https://example.com/converter.py"
        ),
        ReaperConfig(
            lot_uid="integration-test-3",
            converter_uri="https://example.com/converter.py",
            exporter_name="http",
            exporter_config={"upload_url": "https://example.com/upload"}
        )
    ]
    
    results = []
    for i, config in enumerate(configs):
        print(f"Testing configuration {i+1}...")
        result = workflow.run(config)
        results.append(result)
        print(f"Result: {result}")
    
    success_count = sum(1 for r in results if r == "finished")
    print(f"Integration test completed: {success_count}/{len(results)} successful")
    
    return success_count == len(results)


if __name__ == "__main__":
    print("Reaper Workflow Test Suite")
    print("=" * 40)
    
    # Run unit tests
    print("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "=" * 40)
    
    # Run integration test
    integration_success = run_integration_test()
    
    print("\n" + "=" * 40)
    print(f"All tests completed. Integration test: {'PASSED' if integration_success else 'FAILED'}")

//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	// "annout/internal/biz"
	"annout/internal/conf"
	// "annout/internal/data"
	"annout/internal/server"
	"annout/internal/service"
	"annout/workflow"

	// "github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init kratos application.
func wireApp(string, *conf.Server, *conf.Data, log.Logger) (*App, func(), error) {
	// panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, workflow.ProviderSet, newApp))
	panic(wire.Build(server.ProviderSet, service.ProviderSet, workflow.ProviderSet, newApp))
}

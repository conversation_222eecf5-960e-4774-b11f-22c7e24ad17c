package main

import (
	"flag"
	"os"

	"annout/api/client"
	"annout/internal/conf"
	"annout/internal/data"
	"annout/internal/ostore"
	"annout/workflow"
	"annout/workflow/reap"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/env"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	"gitlab.rp.konvery.work/platform/pkg/k8s"
	ilog "gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/otel"
	"gitlab.rp.konvery.work/platform/pkg/wf"
)

var (
	// Name is the name of the compiled software.
	Name string = "annout"
	// Version is the version of the compiled software.
	flagconf string

	id, _ = os.Hostname()
)

func init() {
	flag.StringVar(&flagconf, "conf", "../../configs", "config path, eg: -conf config.yaml")
}

type App struct {
	*kratos.App
	reapAct *reap.Activities
}

func newApp(logger log.Logger, gs *grpc.Server, hs *http.Server, reapAct *reap.Activities) *App {
	return &App{
		App: kratos.New(
			kratos.ID(id),
			kratos.Name(Name),
			kratos.Version(conf.Version),
			kratos.Metadata(map[string]string{}),
			kratos.Logger(logger),
			kratos.Server(
				gs,
				hs,
			),
		),
		reapAct: reapAct,
	}
}

func main() {
	flag.Parse()
	c := config.New(
		config.WithSource(
			file.NewSource(flagconf),
			env.NewSource(),
		),
		config.WithResolver(CustomResolver),
	)
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}
	logger := ilog.GetLogger(&ilog.Config{
		Level:  bc.Otel.Log.Level,
		Format: bc.Otel.Log.Format,
	})
	log.SetLogger(logger)

	switch flag.Arg(0) {
	case "migrate":
		data.Migrate(bc.Data.Database, flag.Arg(1))
		return
	}

	client.Init(&bc)
	app, cleanup, err := wireApp(bc.Workspace, bc.Server, bc.Data, logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	otelcfg := otel.NewOtelCfg(bc.Otel.GetLog(), bc.Otel.GetMetrics(), bc.Otel.GetTracing())
	shutdown, err := otel.InitOtel(Name, conf.Version, otelcfg, logger)
	if err != nil {
		panic(err)
	}
	defer shutdown()

	if k8s.IsInK8s() {
		k8s.InitInCluster()
	}

	ostore.Init(bc.ObjectStorage)
	workflow.Init(bc.Temporal, logger) // for EventsBiz.BackgroundTask
	workflow.InitKtwf(bc.Temporal, bc.Ktwf, logger)
	wf.RegisterActivity(app.reapAct)
	if !bc.Temporal.DisableWorker {
		go wf.Run()
	}

	// start and wait for stop signal
	log.Infof("[main] current version: %s", conf.Version)
	if err := app.Run(); err != nil {
		panic(err)
	}
}

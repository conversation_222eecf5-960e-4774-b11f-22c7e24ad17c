package varylidar

import (
	"path"
	"strings"

	"annout/api/client"
	"annout/internal/biz"
	"annout/workflow/style/common"
	"annout/workflow/types"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/kfs"

	"github.com/samber/lo"
)

func VaryLidarTransform(reaper *biz.Reaper, jobs []*client.Job) (slicer types.Slicer, err error) {
	return &varyLidarStyle{jobs: jobs}, nil
}

type varyLidarStyle struct {
	jobs []*client.Job
}

func (o *varyLidarStyle) Slice() (out []*types.Piece, err error) {
	defer func() { common.FillRawdataURL(o.jobs) }()

	for _, job := range o.jobs {
		out = append(out, lo.Map(job.Annotations, func(ev *anno.ElementAnno, _ int) *types.Piece {
			annosFile := types.AnnosFile
			for _, r := range ev.RawdataAnnos {
				if strings.HasSuffix(r.Name, types.PcdExtension) {
					annosFile = kfs.FileBareName(r.Name) + ".json"
				}
			}
			return &types.Piece{
				Name: path.Join(ev.Name, annosFile),
				Data: &anno.ExportAnnos{
					ElementAnnos: []*anno.ElementAnno{ev},
					JobUid:       job.Uid,
				},
			}
		})...)
	}
	return out, nil
}

package style

import (
	"path"

	"annout/api/client"
	"annout/internal/biz"
	"annout/workflow/style/common"
	"annout/workflow/style/varylidar"
	"annout/workflow/types"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"

	"github.com/samber/lo"
)

func init() {
	// make it the default one
	common.Register("", RawTransform)
	common.Register("raw", RawTransform)
	common.Register("vary_lidar", varylidar.VaryLidarTransform)
}

func RawTransform(reaper *biz.Reaper, jobs []*client.Job) (slicer types.Slicer, err error) {
	return &rawStyle{jobs: jobs}, nil
}

type rawStyle struct {
	jobs []*client.Job
}

func (o *rawStyle) Slice() (out []*types.Piece, err error) {
	defer func() { common.FillRawdataURL(o.jobs) }()

	for _, job := range o.jobs {
		out = append(out, lo.Map(job.Annotations, func(ev *anno.ElementAnno, _ int) *types.Piece {
			return &types.Piece{
				Name: path.Join(ev.Name, types.AnnosFile),
				Data: &anno.ExportAnnos{
					ElementAnnos: []*anno.ElementAnno{ev},
					JobUid:       job.Uid,
				},
			}
		})...)
	}
	return out, nil
}

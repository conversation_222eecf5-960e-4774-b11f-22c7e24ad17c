package ostore

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"time"

	"annout/internal/conf"

	"gitlab.rp.konvery.work/platform/pkg/cloudfront"
	"gitlab.rp.konvery.work/platform/pkg/s3"
)

var (
	cfclis []*cfSigner
	s3cli  *s3.Client
)

type cfSigner struct {
	origin string
	cli    *cloudfront.Signer
}

func InitSigner(oscfg *conf.ObjectStorage) {
	cf := oscfg.Cloudfront
	if cf != nil && cf.Enabled {
		cfclis = make([]*cfSigner, 0, len(cf.Distributions))
		for _, d := range cf.Distributions {
			c := &cloudfront.SignerConfig{
				Origin:    d.Origin,
				URLPrefix: d.UrlPrefix,
				Public:    d.Public,
			}
			if !d.Public {
				c.Expires = cf.Expires.AsDuration()
				c.KeyID = cf.SignKeyId
				c.KeyPEM = cf.SignKey
			}
			cfcli, err := cloudfront.NewSigner(c)
			if err != nil {
				panic(err)
			}
			cfclis = append(cfclis, &cfSigner{origin: d.Origin, cli: cfcli})
		}
	}
	s3cli = NewS3Client(oscfg)
}

func NewS3Client(oscfg *conf.ObjectStorage) *s3.Client {
	s3cfg := oscfg.S3
	if s3cfg == nil {
		return nil
	}
	cred := s3.Credential{
		AccessKey: s3cfg.AccessKey,
		SecretKey: s3cfg.SecretKey,
	}
	cli, err := s3.New(s3cfg.Bucket, &s3.Config{
		PresignExpires: s3cfg.Expires.AsDuration(),
		Profile: &s3.Profile{
			Credential: cred,
		},
	})
	if err != nil {
		panic(err)
	}
	return cli
}

func SignGetURL(ctx context.Context, uri string, fileSize int64, expires time.Duration) (*s3.PresignResult, error) {
	if uri == "" || strings.HasPrefix(uri, "http://") || strings.HasPrefix(uri, "https://") {
		return &s3.PresignResult{URL: uri}, nil
	}

	u, err := url.Parse(uri)
	if err != nil {
		return nil, fmt.Errorf("failed to parse uri %v: %w", uri, err)
	}
	key := u.Path
	// cloudfront can only handle files less than 50GB in the cache mode
	if len(cfclis) > 0 && fileSize < 50*1024*1024*1024 {
		for _, cf := range cfclis {
			if strings.HasPrefix(uri, cf.origin) {
				return cf.cli.SignURL(uri, expires)
			}
		}
		return nil, fmt.Errorf("uri %v does not match any distribution", uri)
	} else {
		key = strings.TrimPrefix(key, "/")
		opt := &s3.PresignOpts{Bucket: u.Host, Expires: expires}
		return s3cli.PresignGetObj(ctx, key, opt)
	}
}

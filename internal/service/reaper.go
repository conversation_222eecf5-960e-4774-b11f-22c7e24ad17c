// source: annout/v1/data-.proto
package service

import (
	"context"

	"annout/api/client"
	"annout/internal/biz"

	"gitlab.rp.konvery.work/platform/apis/annout/v1"
	"gitlab.rp.konvery.work/platform/pkg/errors"

	"google.golang.org/protobuf/types/known/emptypb"
)

func ToBizReaper(d *annout.CreateReaperRequest) *biz.Reaper {
	if d == nil {
		return nil
	}
	o := &biz.Reaper{
		LotUid:     d.LotUid,
		OrderUid:   d.OrderUid,
		DataUid:    d.DataUid,
		Config:     d.Config,
		Ontologies: d.Ontologies,
	}
	return o
}

type ReapersService struct {
	annout.UnimplementedReapersServer
	bg biz.BackgroundTask
}

func NewReapersService(bg biz.BackgroundTask) *ReapersService {
	return &ReapersService{bg: bg}
}

func (o *ReapersService) CreateReaper(ctx context.Context, req *annout.CreateReaperRequest) (*annout.Reaper, error) {
	if req.Config == nil {
		return nil, errors.NewErrEmptyField(errors.WithFields("exporter"))
	}

	data := ToBizReaper(req)

	// start workflow
	err := o.bg.StartReaper(ctx, data)
	if err != nil {
		return nil, err
	}

	return &annout.Reaper{State: biz.ReaperStateOngoing}, nil
}

func (o *ReapersService) GetReaper(ctx context.Context, req *annout.GetReaperRequest) (*annout.Reaper, error) {
	state, err := o.bg.GetReaperState(ctx, req.LotUid)
	if err != nil {
		return nil, err
	}

	return &annout.Reaper{State: state}, nil
}

func (o *ReapersService) ExportLotAnnos(ctx context.Context, req *annout.ExportLotAnnosRequest) (*emptypb.Empty, error) {
	lot, err := client.GetLot(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if req.OrderUid != "" && req.OrderUid != lot.OrderUid {
		return nil, errors.NewErrInvalidField(errors.WithFields("order_uid"))
	}

	reaper := &biz.Reaper{
		LotUid:     lot.Uid,
		OrderUid:   lot.OrderUid,
		DataUid:    lot.DataUid,
		Config:     lot.Out,
		Ontologies: lot.Ontologies,
		Option:     req.Option,
		PhaseCount: int32(len(lot.Phases)),
		Phases:     req.Phases,
	}
	// start workflow
	if err := o.bg.StartReaper(ctx, reaper); err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

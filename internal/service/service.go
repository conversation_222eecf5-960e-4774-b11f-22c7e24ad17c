package service

import (
	"context"

	"annout/api/client"
	"annout/internal/biz"

	"github.com/google/wire"
)

// ProviderSet is service providers.
var ProviderSet = wire.NewSet(NewReapersService, NewConfigsService)

func getCreateScope(op *biz.User, orgUid string) (org, scope string) {
	org = orgUid
	if org == "" {
		org = op.OrgUid
	}
	if org != "" {
		scope = client.GroupScope(org)
	}
	return
}

func getListScope(ctx context.Context, orgUid, creatorUid string) (creator, scope string) {
	if creatorUid != "" {
		return creatorUid, client.UserScope(creatorUid)
	}
	if orgUid != "" {
		return creatorUid, client.GroupScope(orgUid)
	}

	op := biz.UserFromCtx(ctx)
	if client.IsPrivileged(op.GetRole()) {
		return
	}
	return op.GetUid(), client.UserScope(op.GetUid())
}

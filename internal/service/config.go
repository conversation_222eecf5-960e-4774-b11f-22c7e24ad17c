package service

import (
	"context"

	"annout/internal/conf"
	"gitlab.rp.konvery.work/platform/apis/annout/v1"

	"google.golang.org/protobuf/types/known/emptypb"
)

type ConfigsService struct {
	annout.UnimplementedConfigsServer
}

func NewConfigsService() *ConfigsService {
	return &ConfigsService{}
}

func (o *ConfigsService) GetVersion(ctx context.Context, req *emptypb.Empty) (*annout.GetVersionReply, error) {
	return &annout.GetVersionReply{Version: conf.Version}, nil
}

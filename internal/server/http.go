package server

import (
	"strings"

	"annout/internal/conf"
	"annout/internal/service"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/gorilla/handlers"
	"gitlab.rp.konvery.work/platform/apis/annout/v1"
	"gitlab.rp.konvery.work/platform/apis/middleware"
	"gitlab.rp.konvery.work/platform/apis/middleware/common"
)

// NewHTTPServer new a HTTP server.
func NewHTTPServer(c *conf.Server,
	cfgs *service.ConfigsService,
	reapers *service.ReapersService,
	logger log.Logger) *http.Server {
	mws := common.Middlewares(logger,
		middleware.Auth(),
		middleware.LoadUser(noauthFilters),
	)
	var opts = []http.ServerOption{
		http.Middleware(mws...),
	}
	if len(c.CorsOrigins) > 0 {
		opts = append(opts, http.Filter(handlers.CORS(
			handlers.AllowCredentials(),
			handlers.AllowedHeaders([]string{"X-Requested-With", "Content-Type", "Authorization"}),
			handlers.AllowedMethods([]string{"GET", "POST", "PUT", "HEAD", "OPTIONS", "DELETE", "PATCH"}),
			handlers.AllowedOrigins(strings.Split(c.CorsOrigins, ",")),
		)))
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	srv := http.NewServer(opts...)

	handleHealth(srv)
	annout.RegisterConfigsHTTPServer(srv, cfgs)
	// v1.RegisterReapersHTTPServer(srv, reapers)
	return srv
}

func handleHealth(srv *http.Server) {
	route := srv.Route("/")
	route.GET("/healthz", func(ctx http.Context) error {
		ctx.String(200, "OK")
		return nil
	})
}

package server

import (
	"annout/internal/conf"
	"annout/internal/service"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"gitlab.rp.konvery.work/platform/apis/annout/v1"
	"gitlab.rp.konvery.work/platform/apis/middleware"
	"gitlab.rp.konvery.work/platform/apis/middleware/authfilter"
	"gitlab.rp.konvery.work/platform/apis/middleware/common"
)

var unauthWhitelist = []string{
	"/annout.v1.Configs/ListErrors",
}
var noauthFilters = []authfilter.NoauthFilter{authfilter.WhitelistNoauthFilter(unauthWhitelist...)}

// NewGRPCServer new a gRPC server.
func NewGRPCServer(c *conf.Server,
	cfgs *service.ConfigsService,
	reapers *service.ReapersService,
	logger log.Logger) *grpc.Server {
	mws := common.Middlewares(logger,
		middleware.LoadUser(noauthFilters),
	)
	var opts = []grpc.ServerOption{
		grpc.Middleware(mws...),
	}
	if c.Grpc.Network != "" {
		opts = append(opts, grpc.Network(c.Grpc.Network))
	}
	if c.Grpc.Addr != "" {
		opts = append(opts, grpc.Address(c.Grpc.Addr))
	}
	if c.Grpc.Timeout != nil {
		opts = append(opts, grpc.Timeout(c.Grpc.Timeout.AsDuration()))
	}
	srv := grpc.NewServer(opts...)
	annout.RegisterConfigsServer(srv, cfgs)
	annout.RegisterReapersServer(srv, reapers)
	return srv
}

BEGIN;

-- CREATE TABLE reapers
-- (
--     id          BIGSERIAL    NOT NULL PRIMARY KEY,
--     config      J<PERSON>NB        NOT NULL DEFAULT '{}'::JSONB,
--     state       VARCHAR(128) NOT NULL,
--     size        INTEGER      NOT NULL DEFAULT 0,
--     lot_uid     VARCHAR(32)  NOT NULL DEFAULT '',
--     created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
--     updated_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
-- );
-- CREATE INDEX idx_reapers_name ON reapers ((lower(name))) INCLUDE (state, deleted_at);
-- CREATE INDEX idx_reapers_created_at ON reapers (created_at) INCLUDE (state, deleted_at);
-- CREATE INDEX idx_reapers_lot_uid ON reapers (lot_uid) INCLUDE (state, deleted_at);

COMMIT;

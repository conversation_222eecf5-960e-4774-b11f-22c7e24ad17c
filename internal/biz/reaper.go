// source: annout/v1/data-.proto
package biz

import (
	"context"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
)

const (
	ReaperStateOngoing  = "ongoing"
	ReaperStateFinished = "finished"
	ReaperStateFailed   = "failed"
)

// type ReaperConfig = serial.Type[*anno.OutConfig]
// type Ontologies = serial.Type[*anno.Lotontologies]

type Reaper struct {
	LotUid     string              `json:"lot_uid" gorm:"default:null"`
	OrderUid   string              `json:"order_uid" gorm:"default:null"`
	DataUid    string              `json:"data_uid" gorm:"default:null"`
	Config     *anno.OutConfig     `json:"config" gorm:"default:null"`
	Ontologies *anno.Lotontologies `json:"ontologies" gorm:"default:null"`
	Option     anno.ExportOrderAnnosRequest_Option_Enum
	PhaseCount int32
	Phases     []int32
}

type BackgroundTask interface {
	StartReaper(context.Context, *Reaper) error
	GetReaperState(context.Context, string) (string, error)
}

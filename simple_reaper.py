#!/usr/bin/env python3
"""
简化版本的 Reaper - 只实现数据收集和本地保存
功能：收集注释数据 -> 打包保存到本地
"""

import os
import json
import zipfile
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
from dataclasses import dataclass

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class SimpleConfig:
    """简化的配置类"""
    lot_id: str
    phases: List[int] = None
    output_dir: str = "./output"
    
    def __post_init__(self):
        if self.phases is None:
            self.phases = []


class APIClient:
    """API 客户端 - 暂时用占位符实现，后续替换为真实的 curl 调用"""
    
    def __init__(self):
        logger.info("初始化 API 客户端")
    
    def get_lot_info(self, lot_id: str) -> Dict[str, Any]:
        """获取批次信息 - 占位符实现"""
        logger.info(f"获取批次信息: {lot_id}")
        # TODO: 替换为真实的 API 调用
        return {
            "uid": lot_id,
            "name": f"lot-{lot_id}",
            "order_uid": f"order-{lot_id}",
            "phases": [1, 2, 3]
        }
    
    def list_jobs(self, lot_id: str, page: int = 0, page_size: int = 10, phases: List[int] = None) -> Dict[str, Any]:
        """获取作业列表 - 占位符实现"""
        logger.info(f"获取作业列表: lot_id={lot_id}, page={page}, phases={phases}")
        # TODO: 替换为真实的 API 调用
        
        # 模拟分页结束
        if page > 2:
            return {"jobs": []}
        
        # 模拟作业数据
        jobs = []
        for i in range(page_size):
            job_id = page * page_size + i
            jobs.append({
                "uid": f"job-{job_id}",
                "lot_uid": lot_id,
                "idx_in_lot": job_id,
                "state": "finished",
                "annotations": [
                    {
                        "name": f"element-{job_id}-{j}",
                        "index": j,
                        "rawdata_annos": [
                            {
                                "name": f"data-{j}",
                                "objects": [
                                    {
                                        "track_id": f"track-{j}",
                                        "type": "car",
                                        "confidence": 0.95,
                                        "bbox": [100, 100, 200, 200]
                                    }
                                ]
                            }
                        ],
                        "attrs": []
                    } for j in range(3)
                ],
                "job_attrs": [
                    {"name": "worker_id", "values": [f"worker-{job_id}"]}
                ]
            })
        
        return {"jobs": jobs}


class SimpleReaper:
    """简化的 Reaper 实现"""
    
    def __init__(self, api_client: APIClient):
        self.api_client = api_client
    
    def run(self, config: SimpleConfig) -> str:
        """运行简化的工作流"""
        try:
            logger.info(f"开始处理批次: {config.lot_id}")
            
            # 步骤1: 收集注释数据
            annotations = self._collect_annotations(config)
            
            # 步骤2: 保存到本地
            output_file = self._save_to_local(annotations, config)
            
            logger.info(f"处理完成，输出文件: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"处理失败: {e}")
            raise
    
    def _collect_annotations(self, config: SimpleConfig) -> List[Dict[str, Any]]:
        """收集注释数据"""
        logger.info("开始收集注释数据...")
        
        all_annotations = []
        page = 0
        page_size = 10
        
        while True:
            # 获取当前页的作业
            response = self.api_client.list_jobs(
                config.lot_id, 
                page=page, 
                page_size=page_size, 
                phases=config.phases
            )
            
            jobs = response.get("jobs", [])
            if not jobs:
                break
            
            # 处理作业数据
            for job in jobs:
                # 处理注释数据
                self._process_job_annotations(job)
                
                # 收集注释
                for annotation in job.get("annotations", []):
                    all_annotations.append(annotation)
            
            page += 1
            logger.info(f"已处理第 {page} 页，当前收集到 {len(all_annotations)} 个注释")
        
        logger.info(f"注释收集完成，总计: {len(all_annotations)} 个")
        return all_annotations
    
    def _process_job_annotations(self, job: Dict[str, Any]):
        """处理作业注释数据（添加作业属性、确保唯一性等）"""
        # 添加作业属性前缀
        job_attrs = []
        for attr in job.get("job_attrs", []):
            job_attrs.append({
                "name": f"job_attr:{attr['name']}",
                "values": attr["values"]
            })
        
        # 处理每个注释
        for annotation in job.get("annotations", []):
            # 添加作业属性到注释
            annotation["attrs"] = annotation.get("attrs", []) + job_attrs
            
            # 确保 track_id 在批次内唯一
            for rawdata_anno in annotation.get("rawdata_annos", []):
                for obj in rawdata_anno.get("objects", []):
                    if obj.get("track_id"):
                        obj["track_id"] = f"job{job['idx_in_lot']:05d}.{obj['track_id']}"
    
    def _save_to_local(self, annotations: List[Dict[str, Any]], config: SimpleConfig) -> str:
        """保存数据到本地"""
        logger.info("开始保存数据到本地...")
        
        # 创建输出目录
        output_dir = Path(config.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取批次信息
        lot_info = self.api_client.get_lot_info(config.lot_id)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        phases_str = "_".join(map(str, config.phases)) if config.phases else "all"
        
        # 保存 JSON 格式
        json_filename = f"lot_{config.lot_id}_phases_{phases_str}_{timestamp}.json"
        json_path = output_dir / json_filename
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump({
                "lot_info": lot_info,
                "config": {
                    "lot_id": config.lot_id,
                    "phases": config.phases,
                    "export_time": timestamp
                },
                "annotations": annotations,
                "summary": {
                    "total_annotations": len(annotations),
                    "export_time": datetime.now().isoformat()
                }
            }, f, indent=2, ensure_ascii=False)
        
        # 保存 ZIP 格式
        zip_filename = f"lot_{config.lot_id}_phases_{phases_str}_{timestamp}.zip"
        zip_path = output_dir / zip_filename
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 添加 JSON 文件
            zipf.write(json_path, json_filename)
            
            # 添加元数据文件
            meta_data = {
                "lot_id": config.lot_id,
                "phases": config.phases,
                "total_annotations": len(annotations),
                "export_time": datetime.now().isoformat(),
                "format_version": "1.0"
            }
            zipf.writestr("metadata.json", json.dumps(meta_data, indent=2))
            
            # 按页面分割保存注释（便于大数据处理）
            page_size = 100
            for i in range(0, len(annotations), page_size):
                page_annotations = annotations[i:i + page_size]
                page_filename = f"annotations_page_{i//page_size + 1:03d}.json"
                zipf.writestr(page_filename, json.dumps(page_annotations, indent=2, ensure_ascii=False))
        
        # 删除临时 JSON 文件
        json_path.unlink()
        
        logger.info(f"数据已保存到: {zip_path}")
        return str(zip_path)


def main():
    """主函数 - 演示用法"""
    # 初始化
    api_client = APIClient()
    reaper = SimpleReaper(api_client)
    
    # 配置
    config = SimpleConfig(
        lot_id="lot-12345",
        phases=[1, 2],  # 只处理阶段 1 和 2
        output_dir="./exports"
    )
    
    # 运行
    try:
        output_file = reaper.run(config)
        print(f"✅ 导出成功: {output_file}")
    except Exception as e:
        print(f"❌ 导出失败: {e}")


if __name__ == "__main__":
    main()

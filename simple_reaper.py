#!/usr/bin/env python3
"""
简化版本的 Reaper - 只实现数据收集和本地保存
功能：收集注释数据 -> 打包保存到本地
"""

import os
import json
import zipfile
import logging
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# UID 转换相关常量和函数
BASE_CHARS = 'abcdefghijklmnopqrstuvwxyz345678'


def kw_parse_uid(uid: str) -> int:
    """
    Parses a UID string into a bigint (integer).
    This function mimics the behavior of the SQL function kw_parse_uid.
    """
    id_value = 0
    for char_c in uid:
        try:
            char_index = BASE_CHARS.index(char_c)
            id_value = (id_value << 5) + char_index
        except ValueError:
            raise ValueError(f"Error: Character '{char_c}' from UID '{uid}' not found in BASE_CHARS.")
    return id_value


def kw_to_uid(id_value: int) -> str:
    """
    Converts a bigint (integer) into a UID string.
    This function mimics the behavior of the SQL function kw_to_uid.
    """
    uid = ""
    while id_value > 0:
        char_index = id_value & 31
        char_to_prepend = BASE_CHARS[char_index]
        uid = char_to_prepend + uid
        id_value = id_value >> 5
    return uid


@dataclass
class SimpleConfig:
    """简化的配置类"""
    lot_id: str
    phases: List[int] = None
    finished_only: bool = False  # 是否只获取已完成的作业
    output_dir: str = "./output"
    db_path: str = "database.db"  # 数据库路径

    def __post_init__(self):
        if self.phases is None:
            self.phases = []


class DatabaseClient:
    """数据库客户端 - 直接查询 SQL 数据库"""

    def __init__(self, db_path: str):
        self.db_path = db_path
        logger.info(f"初始化数据库客户端: {db_path}")
        # 创建示例数据库（如果不存在）
        self._create_sample_data()

    def _create_sample_data(self):
        """创建示例数据（用于演示）"""
        if not os.path.exists(self.db_path):
            logger.info("创建示例数据库...")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 创建 jobs 表
            cursor.execute('''
                CREATE TABLE jobs (
                    id INTEGER PRIMARY KEY,
                    lot_id INTEGER NOT NULL,
                    idx_in_lot INTEGER NOT NULL DEFAULT 0,
                    elems INTEGER NOT NULL DEFAULT 0,
                    phase INTEGER NOT NULL DEFAULT 0,
                    submits INTEGER NOT NULL DEFAULT 0,
                    finished BOOLEAN NOT NULL DEFAULT false,
                    duration INTEGER NOT NULL DEFAULT 0,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    first_claim TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    last_submit TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 插入示例数据
            lot_id_int = kw_parse_uid("lot-12345")
            sample_jobs = []
            for i in range(50):
                sample_jobs.append((
                    i + 1,  # id
                    lot_id_int,  # lot_id
                    i,  # idx_in_lot
                    3,  # elems
                    (i % 3) + 1,  # phase (1, 2, 3)
                    1,  # submits
                    i % 4 != 0,  # finished (75% finished)
                    1000 + i * 10,  # duration
                ))

            cursor.executemany('''
                INSERT INTO jobs (id, lot_id, idx_in_lot, elems, phase, submits, finished, duration)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', sample_jobs)

            conn.commit()
            conn.close()
            logger.info(f"示例数据库创建完成，插入了 {len(sample_jobs)} 条作业记录")

    def get_lot_info(self, lot_id: str) -> Dict[str, Any]:
        """获取批次信息"""
        logger.info(f"获取批次信息: {lot_id}")
        lot_id_int = kw_parse_uid(lot_id)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 查询批次的基本统计信息
        cursor.execute('''
            SELECT
                COUNT(*) as total_jobs,
                COUNT(CASE WHEN finished = 1 THEN 1 END) as finished_jobs,
                MIN(phase) as min_phase,
                MAX(phase) as max_phase
            FROM jobs
            WHERE lot_id = ?
        ''', (lot_id_int,))

        result = cursor.fetchone()
        conn.close()

        if result and result[0] > 0:
            phases = list(range(result[2], result[3] + 1)) if result[2] and result[3] else []
            return {
                "uid": lot_id,
                "name": f"lot-{lot_id}",
                "order_uid": f"order-{lot_id}",
                "phases": phases,
                "total_jobs": result[0],
                "finished_jobs": result[1]
            }
        else:
            raise ValueError(f"批次 {lot_id} 不存在")

    def list_jobs(self, lot_id: str, page: int = 0, page_size: int = 10,
                  phases: List[int] = None, finished_only: bool = False) -> Dict[str, Any]:
        """获取作业列表"""
        logger.info(f"查询作业: lot_id={lot_id}, page={page}, phases={phases}, finished_only={finished_only}")

        lot_id_int = kw_parse_uid(lot_id)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 构建查询条件
        where_conditions = ["lot_id = ?"]
        params = [lot_id_int]

        if phases:
            placeholders = ",".join("?" * len(phases))
            where_conditions.append(f"phase IN ({placeholders})")
            params.extend(phases)

        if finished_only:
            where_conditions.append("finished = 1")

        where_clause = " AND ".join(where_conditions)

        # 查询作业
        offset = page * page_size
        query = f'''
            SELECT id, lot_id, idx_in_lot, elems, phase, submits, finished, duration
            FROM jobs
            WHERE {where_clause}
            ORDER BY idx_in_lot
            LIMIT ? OFFSET ?
        '''

        cursor.execute(query, params + [page_size, offset])
        rows = cursor.fetchall()
        conn.close()

        # 转换为作业格式
        jobs = []
        for row in rows:
            job_id, lot_id_db, idx_in_lot, elems, phase, submits, finished, duration = row

            # 生成模拟的注释数据
            annotations = []
            for j in range(elems):
                annotations.append({
                    "name": f"element-{idx_in_lot}-{j}",
                    "index": j,
                    "rawdata_annos": [
                        {
                            "name": f"data-{j}",
                            "objects": [
                                {
                                    "track_id": f"track-{j}",
                                    "type": "car",
                                    "confidence": 0.95,
                                    "bbox": [100, 100, 200, 200]
                                }
                            ]
                        }
                    ],
                    "attrs": []
                })

            jobs.append({
                "uid": f"job-{job_id}",
                "lot_uid": lot_id,
                "idx_in_lot": idx_in_lot,
                "phase": phase,
                "finished": bool(finished),
                "elems": elems,
                "annotations": annotations,
                "job_attrs": [
                    {"name": "phase", "values": [str(phase)]},
                    {"name": "duration", "values": [str(duration)]},
                    {"name": "finished", "values": [str(finished)]}
                ]
            })

        return {"jobs": jobs}


class SimpleReaper:
    """简化的 Reaper 实现"""

    def __init__(self, db_client: DatabaseClient):
        self.db_client = db_client
    
    def run(self, config: SimpleConfig) -> str:
        """运行简化的工作流"""
        try:
            logger.info(f"开始处理批次: {config.lot_id}")
            
            # 步骤1: 收集注释数据
            annotations = self._collect_annotations(config)
            
            # 步骤2: 保存到本地
            output_file = self._save_to_local(annotations, config)
            
            logger.info(f"处理完成，输出文件: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"处理失败: {e}")
            raise
    
    def _collect_annotations(self, config: SimpleConfig) -> List[Dict[str, Any]]:
        """收集注释数据"""
        logger.info("开始收集注释数据...")
        
        all_annotations = []
        page = 0
        page_size = 10
        
        while True:
            # 获取当前页的作业
            response = self.db_client.list_jobs(
                config.lot_id,
                page=page,
                page_size=page_size,
                phases=config.phases,
                finished_only=config.finished_only
            )
            
            jobs = response.get("jobs", [])
            if not jobs:
                break
            
            # 处理作业数据
            for job in jobs:
                # 处理注释数据
                self._process_job_annotations(job)
                
                # 收集注释
                for annotation in job.get("annotations", []):
                    all_annotations.append(annotation)
            
            page += 1
            logger.info(f"已处理第 {page} 页，当前收集到 {len(all_annotations)} 个注释")
        
        logger.info(f"注释收集完成，总计: {len(all_annotations)} 个")
        return all_annotations
    
    def _process_job_annotations(self, job: Dict[str, Any]):
        """处理作业注释数据（添加作业属性、确保唯一性等）"""
        # 添加作业属性前缀
        job_attrs = []
        for attr in job.get("job_attrs", []):
            job_attrs.append({
                "name": f"job_attr:{attr['name']}",
                "values": attr["values"]
            })
        
        # 处理每个注释
        for annotation in job.get("annotations", []):
            # 添加作业属性到注释
            annotation["attrs"] = annotation.get("attrs", []) + job_attrs
            
            # 确保 track_id 在批次内唯一
            for rawdata_anno in annotation.get("rawdata_annos", []):
                for obj in rawdata_anno.get("objects", []):
                    if obj.get("track_id"):
                        obj["track_id"] = f"job{job['idx_in_lot']:05d}.{obj['track_id']}"
    
    def _save_to_local(self, annotations: List[Dict[str, Any]], config: SimpleConfig) -> str:
        """保存数据到本地"""
        logger.info("开始保存数据到本地...")
        
        # 创建输出目录
        output_dir = Path(config.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取批次信息
        lot_info = self.db_client.get_lot_info(config.lot_id)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        phases_str = "_".join(map(str, config.phases)) if config.phases else "all"
        
        # 保存 JSON 格式
        json_filename = f"lot_{config.lot_id}_phases_{phases_str}_{timestamp}.json"
        json_path = output_dir / json_filename
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump({
                "lot_info": lot_info,
                "config": {
                    "lot_id": config.lot_id,
                    "phases": config.phases,
                    "export_time": timestamp
                },
                "annotations": annotations,
                "summary": {
                    "total_annotations": len(annotations),
                    "export_time": datetime.now().isoformat()
                }
            }, f, indent=2, ensure_ascii=False)
        
        # 保存 ZIP 格式
        zip_filename = f"lot_{config.lot_id}_phases_{phases_str}_{timestamp}.zip"
        zip_path = output_dir / zip_filename
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 添加 JSON 文件
            zipf.write(json_path, json_filename)
            
            # 添加元数据文件
            meta_data = {
                "lot_id": config.lot_id,
                "phases": config.phases,
                "total_annotations": len(annotations),
                "export_time": datetime.now().isoformat(),
                "format_version": "1.0"
            }
            zipf.writestr("metadata.json", json.dumps(meta_data, indent=2))
            
            # 按页面分割保存注释（便于大数据处理）
            page_size = 100
            for i in range(0, len(annotations), page_size):
                page_annotations = annotations[i:i + page_size]
                page_filename = f"annotations_page_{i//page_size + 1:03d}.json"
                zipf.writestr(page_filename, json.dumps(page_annotations, indent=2, ensure_ascii=False))
        
        # 删除临时 JSON 文件
        json_path.unlink()
        
        logger.info(f"数据已保存到: {zip_path}")
        return str(zip_path)


def main():
    """主函数 - 演示用法"""
    # 初始化
    db_client = DatabaseClient("./sample_database.db")
    reaper = SimpleReaper(db_client)

    # 配置
    config = SimpleConfig(
        lot_id="lot-12345",
        phases=[1, 2],  # 只处理阶段 1 和 2
        finished_only=True,  # 只获取已完成的作业
        output_dir="./exports",
        db_path="./sample_database.db"
    )

    # 运行
    try:
        output_file = reaper.run(config)
        print(f"✅ 导出成功: {output_file}")
    except Exception as e:
        print(f"❌ 导出失败: {e}")


if __name__ == "__main__":
    main()

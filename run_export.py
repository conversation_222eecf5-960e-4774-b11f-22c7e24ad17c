#!/usr/bin/env python3
"""
运行数据导出的脚本
支持命令行参数和配置文件
"""

import json
import argparse
import sys
from pathlib import Path
from simple_reaper import SimpleReaper, SimpleConfig, DatabaseClient, DatabaseConfig


def load_config_from_file(config_file: str) -> SimpleConfig:
    """从配置文件加载配置"""
    with open(config_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 数据库配置
    db_data = data.get("database", {})
    db_config = DatabaseConfig(
        host=db_data.get("host", "localhost"),
        port=db_data.get("port", 5432),
        user=db_data.get("user", "root"),
        password=db_data.get("password", "root"),
        database=db_data.get("database", "anno")
    )

    # 支持单个或多个 lot_id
    lot_ids = data.get("lot_ids", [])
    if not lot_ids and "lot_id" in data:
        lot_ids = [data["lot_id"]]

    return SimpleConfig(
        lot_ids=lot_ids,
        phases=data.get("phases", []),
        finished_only=data.get("finished_only", False),
        output_dir=data.get("output_dir", "./exports"),
        db_config=db_config
    )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="简化版数据导出工具")
    parser.add_argument("--lot-ids", nargs="+", help="批次ID列表，例如: --lot-ids abc def ghi")
    parser.add_argument("--phases", nargs="+", type=int, help="处理阶段，例如: --phases 1 2 3")
    parser.add_argument("--finished-only", action="store_true", help="只获取已完成的作业")
    parser.add_argument("--output-dir", default="./exports", help="输出目录")
    parser.add_argument("--db-host", default="localhost", help="数据库主机")
    parser.add_argument("--db-port", type=int, default=5432, help="数据库端口")
    parser.add_argument("--db-user", default="root", help="数据库用户名")
    parser.add_argument("--db-password", default="root", help="数据库密码")
    parser.add_argument("--db-name", default="anno", help="数据库名")
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    # 确定配置来源
    if args.config:
        # 从配置文件加载
        if not Path(args.config).exists():
            print(f"❌ 配置文件不存在: {args.config}")
            sys.exit(1)
        
        config = load_config_from_file(args.config)
        print(f"📄 使用配置文件: {args.config}")
        
    elif args.lot_ids:
        # 从命令行参数创建配置
        db_config = DatabaseConfig(
            host=args.db_host,
            port=args.db_port,
            user=args.db_user,
            password=args.db_password,
            database=args.db_name
        )

        config = SimpleConfig(
            lot_ids=args.lot_ids,
            phases=args.phases or [],
            finished_only=args.finished_only,
            output_dir=args.output_dir,
            db_config=db_config
        )
        print(f"⚙️  使用命令行参数")

    else:
        print("❌ 请提供 --lot-ids 或 --config 参数")
        parser.print_help()
        sys.exit(1)
    
    # 显示配置信息
    print(f"📋 配置信息:")
    print(f"   批次ID: {', '.join(config.lot_ids)}")
    print(f"   处理阶段: {config.phases if config.phases else '全部'}")
    print(f"   只获取已完成: {'是' if config.finished_only else '否'}")
    print(f"   输出目录: {config.output_dir}")
    print(f"   数据库: {config.db_config.host}:{config.db_config.port}/{config.db_config.database}")
    print()
    
    # 初始化并运行
    try:
        db_client = DatabaseClient(config.db_config)
        reaper = SimpleReaper(db_client)

        output_files = reaper.run(config)

        print(f"✅ 导出成功!")
        print(f"📁 输出文件 ({len(output_files)} 个):")
        for file in output_files:
            print(f"   - {file}")
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
运行数据导出的脚本
支持命令行参数和配置文件
"""

import json
import argparse
import sys
from pathlib import Path
from simple_reaper import SimpleReaper, SimpleConfig, DatabaseClient


def load_config_from_file(config_file: str) -> SimpleConfig:
    """从配置文件加载配置"""
    with open(config_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    return SimpleConfig(
        lot_id=data["lot_id"],
        phases=data.get("phases", []),
        finished_only=data.get("finished_only", False),
        output_dir=data.get("output_dir", "./exports"),
        db_path=data.get("db_path", "./database.db")
    )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="简化版数据导出工具")
    parser.add_argument("--lot-id", help="批次ID")
    parser.add_argument("--phases", nargs="+", type=int, help="处理阶段，例如: --phases 1 2 3")
    parser.add_argument("--finished-only", action="store_true", help="只获取已完成的作业")
    parser.add_argument("--output-dir", default="./exports", help="输出目录")
    parser.add_argument("--db-path", default="./database.db", help="数据库路径")
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    # 确定配置来源
    if args.config:
        # 从配置文件加载
        if not Path(args.config).exists():
            print(f"❌ 配置文件不存在: {args.config}")
            sys.exit(1)
        
        config = load_config_from_file(args.config)
        print(f"📄 使用配置文件: {args.config}")
        
    elif args.lot_id:
        # 从命令行参数创建配置
        config = SimpleConfig(
            lot_id=args.lot_id,
            phases=args.phases or [],
            finished_only=args.finished_only,
            output_dir=args.output_dir,
            db_path=args.db_path
        )
        print(f"⚙️  使用命令行参数")
        
    else:
        print("❌ 请提供 --lot-id 或 --config 参数")
        parser.print_help()
        sys.exit(1)
    
    # 显示配置信息
    print(f"📋 配置信息:")
    print(f"   批次ID: {config.lot_id}")
    print(f"   处理阶段: {config.phases if config.phases else '全部'}")
    print(f"   只获取已完成: {'是' if config.finished_only else '否'}")
    print(f"   输出目录: {config.output_dir}")
    print(f"   数据库路径: {config.db_path}")
    print()
    
    # 初始化并运行
    try:
        db_client = DatabaseClient(config.db_path)
        reaper = SimpleReaper(db_client)
        
        output_file = reaper.run(config)
        
        print(f"✅ 导出成功!")
        print(f"📁 输出文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

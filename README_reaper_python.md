# Reaper Workflow Python Implementation

这是基于原始 Go 语言 StartReaper 任务流的 Python 实现。它简化了原始工作流的复杂性，但保持了核心功能。

## 功能概述

这个 Python 实现模拟了原始 Go 工作流的以下核心功能：

1. **注释收集** (ReapCollectAnnos) - 从 API 获取作业和注释数据
2. **注释保存** (ReapSaveAnnos) - 将注释打包成 ZIP 文件并上传
3. **注释转换** (可选) - 运行自定义转换脚本
4. **转换后保存** (ReapSaveConvertedAnnos) - 保存转换后的注释
5. **注释导出** (ReapExportConvertedAnnos) - 导出到外部系统

## 文件结构

```
reaper_workflow.py      # 主要的工作流实现
reaper_example.py       # 使用示例
README_reaper_python.md # 本文档
```

## 依赖要求

```bash
pip install requests
```

## 核心类说明

### ReaperConfig
配置类，包含工作流的所有参数：

```python
@dataclass
class ReaperConfig:
    lot_uid: str              # 批次 UID
    order_uid: str = ""       # 订单 UID
    data_uid: str = ""        # 数据 UID
    encoder: str = "json"     # 编码器类型
    style: str = "raw"        # 样式类型
    option: ExportOption = ExportOption.ALL  # 导出选项
    phases: List[int] = None  # 处理阶段
    converter_uri: str = ""   # 转换脚本 URI
    exporter_name: str = ""   # 导出器名称
    exporter_config: Dict[str, Any] = None  # 导出器配置
```

### MockAPIClient
模拟 API 客户端，在实际使用中应该替换为真实的 API 客户端：

```python
class MockAPIClient:
    def get_lot(self, lot_uid: str) -> Dict[str, Any]
    def list_jobs(self, lot_uid: str, ...) -> Dict[str, Any]
    def create_file(self, name: str, org_uid: str) -> Dict[str, Any]
    def finish_file_upload(self, file_uid: str) -> bool
    def set_lot_anno_result(self, lot_uid: str, file_uri: str) -> bool
    def set_order_anno_result(self, order_uid: str, file_uri: str) -> bool
```

### ReaperWorkflow
主要的工作流类：

```python
class ReaperWorkflow:
    def run(self, config: ReaperConfig) -> str
    def _collect_annotations(self, data_dir: str, config: ReaperConfig)
    def _save_annotations(self, data_dir: str, config: ReaperConfig, is_converted: bool = False) -> str
    def _run_converter(self, input_dir: str, output_dir: str, config: ReaperConfig)
    def _export_annotations(self, data_dir: str, config: ReaperConfig)
```

## 使用示例

### 基本用法

```python
from reaper_workflow import ReaperWorkflow, ReaperConfig, ExportOption, MockAPIClient

# 初始化 API 客户端
api_client = MockAPIClient("http://localhost:8080")

# 创建工作流实例
workflow = ReaperWorkflow(api_client)

# 配置
config = ReaperConfig(
    lot_uid="lot-12345",
    order_uid="order-67890",
    data_uid="data-abcdef",
    encoder="json",
    style="raw",
    option=ExportOption.ALL,
    phases=[1, 2, 3]
)

# 运行工作流
result = workflow.run(config)
print(f"工作流结果: {result}")
```

### 带转换的工作流

```python
config = ReaperConfig(
    lot_uid="lot-12345",
    order_uid="order-67890",
    data_uid="data-abcdef",
    converter_uri="https://example.com/converter.py",
    exporter_name="http",
    exporter_config={
        "upload_url": "https://external-system.com/upload",
        "headers": {"Authorization": "Bearer token123"}
    }
)

result = workflow.run(config)
```

### 从配置文件加载

```python
import json

# 加载配置
with open("config.json", "r") as f:
    config_data = json.load(f)

config = ReaperConfig(**config_data)
result = workflow.run(config)
```

## 运行示例

```bash
# 运行主要示例
python reaper_example.py

# 运行单个工作流
python reaper_workflow.py
```

## 与原始 Go 实现的对比

| 功能 | Go 实现 | Python 实现 |
|------|---------|-------------|
| 工作流引擎 | Temporal | 简单的顺序执行 |
| 容器化转换 | Kubernetes Job | 本地 Python 脚本 |
| 存储 | 工作流卷 | 临时目录 |
| API 客户端 | gRPC/HTTP | HTTP (模拟) |
| 错误处理 | Temporal 重试 | 基本异常处理 |
| 并发 | Temporal Activities | 顺序执行 |

## 扩展建议

要将此实现用于生产环境，建议进行以下改进：

1. **真实 API 客户端**: 替换 MockAPIClient 为真实的 API 客户端
2. **错误处理**: 添加更强大的错误处理和重试机制
3. **并发处理**: 添加异步处理能力
4. **配置管理**: 使用配置文件或环境变量
5. **日志记录**: 改进日志记录和监控
6. **测试**: 添加单元测试和集成测试
7. **容器化**: 支持 Docker 容器中的转换脚本
8. **状态管理**: 添加工作流状态持久化

## 注意事项

- 这是一个简化的实现，主要用于演示和理解原始工作流
- 在生产环境中使用前需要进行适当的修改和测试
- API 客户端是模拟的，需要根据实际 API 进行调整
- 文件上传功能被简化，实际使用时需要实现真实的上传逻辑

## 许可证

此实现仅供学习和参考使用。

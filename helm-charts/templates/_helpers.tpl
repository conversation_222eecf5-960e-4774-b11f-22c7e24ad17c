{{/*
Expand the name of the chart.
*/}}
{{- define "appx.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "appx.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "appx.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "appx.labels" -}}
helm.sh/chart: {{ include "appx.chart" . }}
{{ include "appx.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "appx.selectorLabels" -}}
app.kubernetes.io/name: {{ include "appx.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "appx.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "appx.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}


{{/*
Mapping database secret to environment variables
*/}}
{{- define "helper.secretDBToEnv" -}}
- name: data.database.username
  valueFrom:
    secretKeyRef:
      name: {{ .Values.secretDBName }}
      key: username
- name: data.database.password
  valueFrom:
    secretKeyRef:
      name: {{ .Values.secretDBName }}
      key: password
- name: data.database.endpoint
  valueFrom:
    secretKeyRef:
      name: {{ .Values.secretDBName }}
      key: endpoint
- name: data.database.port
  value: "0"
#  valueFrom:
#    secretKeyRef:
#      name: {{ .Values.secretDBName }}
#      key: port
- name: data.database.database
  value: {{ .Values.database.DATABASE }}
- name: data.database.source
  value: {{ .Values.database.SOURCE }}
- name: data.database.options
  value: ""
{{- end }}


{{/*
Mapping extra environment variables
*/}}
{{- define "helper.envVarsToEnv" -}}
{{- range $key, $value := .Values.envVars }}
- name: {{ $key }}
  value: {{ $value | quote }}
{{- end}}
{{- end }}

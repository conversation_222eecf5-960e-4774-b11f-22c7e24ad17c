apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "appx.fullname" . }}
  labels:
    {{- include "appx.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "appx.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "appx.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "appx.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            {{- toYaml .Values.image.svcCommand | nindent 12 }}
          ports:
            - name: http
              containerPort: {{ .Values.service.httpPort }}
              protocol: TCP
            - name: grpc
              containerPort:  {{ .Values.service.grpcPort }}
              protocol: TCP
            - name: metrics
              containerPort: {{ .Values.service.metrics.port }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /healthz
              port: http
              # httpHeaders:
              #   - name: Host
              #     value: {{ .Values.hostName }}
          readinessProbe:
            httpGet:
              path: /healthz
              port: http
              # httpHeaders:
              #   - name: Host
              #     value: {{ .Values.hostName }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          envFrom:
            - secretRef:
                name: {{ .Values.secretS3 }}
          env:
            {{- include "helper.secretDBToEnv" . | nindent 12 }}
            {{- include "helper.envVarsToEnv" . | nindent 12 }}
            - name: image
              value: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
            - name: command
              value: {{ .Values.image.svcCommand | mustToJson | quote }}
            - name: serviceAccountName
              value: {{ include "appx.serviceAccountName" . }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}

{"basic_config": {"lot_uid": "lot-example-001", "order_uid": "order-example-001", "data_uid": "data-example-001", "encoder": "json", "style": "raw", "option": "all", "phases": [1, 2, 3]}, "conversion_config": {"lot_uid": "lot-convert-001", "order_uid": "order-convert-001", "data_uid": "data-convert-001", "encoder": "json", "style": "raw", "option": "finished", "phases": [1, 2], "converter_uri": "https://example.com/converter.py"}, "export_config": {"lot_uid": "lot-export-001", "order_uid": "order-export-001", "data_uid": "data-export-001", "encoder": "json", "style": "raw", "option": "finished", "phases": [1, 2, 3], "converter_uri": "https://example.com/converter.py", "exporter_name": "http", "exporter_config": {"upload_url": "https://external-system.com/api/upload", "headers": {"Authorization": "Bearer your-api-token-here", "Content-Type": "application/zip", "X-Client-ID": "reaper-python"}}}, "batch_configs": [{"lot_uid": "lot-batch-001", "order_uid": "order-batch-001", "data_uid": "data-batch-001", "encoder": "json", "style": "raw", "option": "all", "phases": [1, 2, 3]}, {"lot_uid": "lot-batch-002", "order_uid": "order-batch-002", "data_uid": "data-batch-002", "encoder": "json", "style": "vary_lidar", "option": "finished", "phases": [1, 2]}, {"lot_uid": "lot-batch-003", "order_uid": "order-batch-003", "data_uid": "data-batch-003", "encoder": "json", "style": "raw", "option": "finished", "phases": [2, 3], "converter_uri": "https://example.com/special-converter.py", "exporter_name": "http", "exporter_config": {"upload_url": "https://special-system.com/upload", "headers": {"Authorization": "Bearer special-token"}}}]}
#!/usr/bin/env python3
"""
Python implementation of the StartReaper workflow
Simplified version that mimics the Go implementation functionality
"""

import os
import json
import zipfile
import requests
import logging
import tempfile
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ReaperState(Enum):
    ONGOING = "ongoing"
    FINISHED = "finished"
    FAILED = "failed"


class ExportOption(Enum):
    ALL = "all"
    FINISHED = "finished"


@dataclass
class ReaperConfig:
    """Configuration for the reaper workflow"""
    lot_uid: str
    order_uid: str = ""
    data_uid: str = ""
    encoder: str = "json"  # json, yaml, etc.
    style: str = "raw"     # raw, vary_lidar, etc.
    option: ExportOption = ExportOption.ALL
    phases: List[int] = None
    converter_uri: str = ""
    exporter_name: str = ""
    exporter_config: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.phases is None:
            self.phases = []
        if self.exporter_config is None:
            self.exporter_config = {}


class MockAPIClient:
    """Mock API client to simulate the Go client functionality"""
    
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def get_lot(self, lot_uid: str) -> Dict[str, Any]:
        """Get lot information"""
        logger.info(f"Getting lot: {lot_uid}")
        # Mock response - in real implementation, this would call the actual API
        return {
            "uid": lot_uid,
            "name": f"lot-{lot_uid}",
            "order_uid": f"order-{lot_uid}",
            "data_uid": f"data-{lot_uid}",
            "org_uid": f"org-{lot_uid}",
            "phases": [1, 2, 3],
            "ins_cnt": 100
        }
    
    def list_jobs(self, lot_uid: str, page: int = 0, page_size: int = 10, 
                  job_state: str = "", phases: List[int] = None) -> Dict[str, Any]:
        """List jobs for a lot"""
        logger.info(f"Listing jobs for lot {lot_uid}, page {page}")
        # Mock response - simulate job data
        if page > 2:  # Simulate pagination end
            return {"jobs": []}
        
        jobs = []
        for i in range(page_size):
            job_id = page * page_size + i
            jobs.append({
                "uid": f"job-{job_id}",
                "lot_uid": lot_uid,
                "idx_in_lot": job_id,
                "state": job_state or "finished",
                "annotations": [
                    {
                        "name": f"element-{job_id}-{j}",
                        "index": j,
                        "rawdata_annos": [
                            {
                                "name": f"data-{j}",
                                "objects": [
                                    {
                                        "track_id": f"track-{j}",
                                        "type": "car",
                                        "confidence": 0.95
                                    }
                                ]
                            }
                        ],
                        "attrs": []
                    } for j in range(3)
                ],
                "job_attrs": []
            })
        
        return {"jobs": jobs}
    
    def create_file(self, name: str, org_uid: str) -> Dict[str, Any]:
        """Create file for upload"""
        logger.info(f"Creating file: {name}")
        file_uid = f"file-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        return {
            "file": {
                "uid": file_uid,
                "uri": f"s3://bucket/{file_uid}/{name}"
            },
            "upload_urls": [f"https://upload.example.com/{file_uid}"]
        }
    
    def finish_file_upload(self, file_uid: str) -> bool:
        """Finish file upload"""
        logger.info(f"Finishing file upload: {file_uid}")
        return True
    
    def set_lot_anno_result(self, lot_uid: str, file_uri: str) -> bool:
        """Set lot annotation result"""
        logger.info(f"Setting lot {lot_uid} anno result: {file_uri}")
        return True
    
    def set_order_anno_result(self, order_uid: str, file_uri: str) -> bool:
        """Set order annotation result"""
        logger.info(f"Setting order {order_uid} anno result: {file_uri}")
        return True


class ReaperWorkflow:
    """Main workflow class that implements the reaper functionality"""
    
    def __init__(self, api_client: MockAPIClient):
        self.api_client = api_client
        self.work_dir = None
    
    def run(self, config: ReaperConfig) -> str:
        """Run the complete reaper workflow"""
        try:
            logger.info(f"Starting reaper workflow for lot: {config.lot_uid}")
            
            # Create working directory
            self.work_dir = tempfile.mkdtemp(prefix="reaper_")
            data_dir = os.path.join(self.work_dir, "data")
            os.makedirs(data_dir, mode=0o755)
            
            # Step 1: Collect annotations
            self._collect_annotations(data_dir, config)
            
            # Step 2: Save original annotations
            original_file_uri = self._save_annotations(data_dir, config, is_converted=False)
            
            # Step 3: Convert annotations (if converter script provided)
            converted_file_uri = None
            if config.converter_uri:
                convert_dir = os.path.join(self.work_dir, "convert")
                os.makedirs(convert_dir, mode=0o755)
                
                self._run_converter(data_dir, convert_dir, config)
                converted_file_uri = self._save_annotations(convert_dir, config, is_converted=True)
                self._export_annotations(convert_dir, config)
            
            logger.info("Reaper workflow completed successfully")
            return ReaperState.FINISHED.value
            
        except Exception as e:
            logger.error(f"Reaper workflow failed: {e}")
            return ReaperState.FAILED.value
        finally:
            # Cleanup
            if self.work_dir and os.path.exists(self.work_dir):
                shutil.rmtree(self.work_dir)
    
    def _collect_annotations(self, data_dir: str, config: ReaperConfig):
        """Collect annotations from the API (equivalent to ReapCollectAnnos)"""
        logger.info("Collecting annotations...")
        
        page = 0
        page_size = 10
        job_state = "finished" if config.option == ExportOption.FINISHED else ""
        
        all_annotations = []
        
        while True:
            # Get jobs for this page
            response = self.api_client.list_jobs(
                config.lot_uid, page, page_size, job_state, config.phases
            )
            
            jobs = response.get("jobs", [])
            if not jobs:
                break
            
            # Process jobs and collect annotations
            for job in jobs:
                # Patch annotations (add job attributes, ensure unique track IDs)
                self._patch_annotations(job)
                all_annotations.extend(job.get("annotations", []))
            
            page += 1
        
        # Save annotations to files
        annos_dir = os.path.join(data_dir, "annos")
        os.makedirs(annos_dir, exist_ok=True)
        
        for i, annotation in enumerate(all_annotations):
            filename = f"annotation_{i:06d}.json"
            filepath = os.path.join(annos_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(annotation, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Collected {len(all_annotations)} annotations")
    
    def _patch_annotations(self, job: Dict[str, Any]):
        """Patch annotations with job attributes and unique track IDs"""
        job_attrs = []
        for attr in job.get("job_attrs", []):
            job_attrs.append({
                "name": f"konv:job_attr:{attr['name']}",
                "values": attr["values"]
            })
        
        for annotation in job.get("annotations", []):
            # Add job attributes to element attributes
            annotation["attrs"] = annotation.get("attrs", []) + job_attrs
            
            # Ensure track IDs are unique within the lot
            for rawdata_anno in annotation.get("rawdata_annos", []):
                for obj in rawdata_anno.get("objects", []):
                    if obj.get("track_id"):
                        obj["track_id"] = f"konv:job{job['idx_in_lot']:05d}.{obj['track_id']}"

    def _save_annotations(self, data_dir: str, config: ReaperConfig, is_converted: bool = False) -> str:
        """Save annotations to ZIP file and upload (equivalent to ReapSaveAnnos)"""
        logger.info(f"Saving {'converted' if is_converted else 'original'} annotations...")

        # Get lot information
        lot = self.api_client.get_lot(config.lot_uid)

        # Create ZIP file
        zip_filename = "annotations.zip"
        zip_path = os.path.join(self.work_dir, zip_filename)

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Add metadata if not converted
            if not is_converted:
                meta = {
                    "base_on_uid": config.data_uid,
                    "related_lot": {
                        "uid": config.lot_uid,
                        "name": lot["name"]
                    },
                    "metadata": {},
                    "metafiles": []
                }
                zipf.writestr("meta.json", json.dumps(meta, indent=2))

            # Add all files from data directory
            for root, dirs, files in os.walk(data_dir):
                for file in files:
                    if file == zip_filename:
                        continue

                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, data_dir)
                    zipf.write(file_path, arc_path)

        # Upload ZIP file
        suffix = "-convert" if is_converted else ""
        filename = f"{lot['name']}_lot-{lot['uid']}_order-{lot['order_uid']}_ins-{lot['ins_cnt']}_{datetime.now().strftime('%y%m%d-%H%M')}{suffix}.zip"

        file_info = self.api_client.create_file(filename, lot["org_uid"])

        # Simulate upload
        upload_url = file_info["upload_urls"][0]
        logger.info(f"Uploading to: {upload_url}")

        # In real implementation, you would upload the file here
        # with open(zip_path, 'rb') as f:
        #     response = requests.put(upload_url, data=f, headers={'Content-Type': 'application/zip'})
        #     response.raise_for_status()

        # Finish upload
        self.api_client.finish_file_upload(file_info["file"]["uid"])

        # Update lot/order with result URI
        file_uri = file_info["file"]["uri"]
        if config.order_uid:
            self.api_client.set_order_anno_result(config.order_uid, file_uri)

        if not is_converted:
            self.api_client.set_lot_anno_result(config.lot_uid, file_uri)

        # Cleanup
        os.remove(zip_path)

        logger.info(f"Annotations saved successfully: {file_uri}")
        return file_uri

    def _run_converter(self, input_dir: str, output_dir: str, config: ReaperConfig):
        """Run converter script (equivalent to kubejob convertData)"""
        logger.info(f"Running converter script: {config.converter_uri}")

        # Download converter script
        converter_script = os.path.join(output_dir, "main.py")
        logger.info(f"Downloading converter script to: {converter_script}")

        # In real implementation, download the script from config.converter_uri
        # response = requests.get(config.converter_uri)
        # response.raise_for_status()
        # with open(converter_script, 'w') as f:
        #     f.write(response.text)

        # For demo, create a simple converter script
        demo_script = '''#!/usr/bin/env python3
import os
import json
import shutil
import sys

def convert_annotations(input_dir, output_dir):
    """Simple converter that copies and modifies annotations"""
    print(f"Converting from {input_dir} to {output_dir}")

    # Copy all files
    if os.path.exists(input_dir):
        shutil.copytree(input_dir, output_dir, dirs_exist_ok=True)

    # Modify annotations (example: add converted flag)
    annos_dir = os.path.join(output_dir, "annos")
    if os.path.exists(annos_dir):
        for filename in os.listdir(annos_dir):
            if filename.endswith('.json'):
                filepath = os.path.join(annos_dir, filename)
                with open(filepath, 'r') as f:
                    data = json.load(f)

                # Add converted flag
                data['converted'] = True
                data['conversion_timestamp'] = '2024-01-01T00:00:00Z'

                with open(filepath, 'w') as f:
                    json.dump(data, f, indent=2)

    print("Conversion completed")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python main.py <input_dir> <output_dir>")
        sys.exit(1)

    convert_annotations(sys.argv[1], sys.argv[2])
'''

        with open(converter_script, 'w') as f:
            f.write(demo_script)

        # Run converter script
        import subprocess
        result = subprocess.run([
            'python3', converter_script, input_dir, output_dir
        ], capture_output=True, text=True)

        if result.returncode != 0:
            raise Exception(f"Converter script failed: {result.stderr}")

        logger.info("Converter script completed successfully")

    def _export_annotations(self, data_dir: str, config: ReaperConfig):
        """Export annotations to external system (equivalent to ReapExportConvertedAnnos)"""
        if not config.exporter_name:
            logger.info("No exporter configured, skipping export")
            return

        logger.info(f"Exporting annotations using: {config.exporter_name}")

        # Create ZIP file for export
        zip_path = os.path.join(self.work_dir, "export.zip")
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(data_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, data_dir)
                    zipf.write(file_path, arc_path)

        # Export based on exporter type
        if config.exporter_name == "http":
            self._export_http(zip_path, config.exporter_config)
        else:
            logger.warning(f"Unknown exporter: {config.exporter_name}")

        # Cleanup
        os.remove(zip_path)

    def _export_http(self, zip_path: str, exporter_config: Dict[str, Any]):
        """Export via HTTP"""
        upload_url = exporter_config.get("upload_url")
        headers = exporter_config.get("headers", {})

        if not upload_url:
            raise Exception("HTTP exporter requires upload_url")

        logger.info(f"Exporting to HTTP endpoint: {upload_url}")

        # In real implementation, upload the file
        # with open(zip_path, 'rb') as f:
        #     response = requests.put(upload_url, data=f, headers=headers)
        #     response.raise_for_status()

        logger.info("HTTP export completed successfully")


def main():
    """Main function to demonstrate the workflow"""
    # Initialize API client
    api_client = MockAPIClient()

    # Create workflow instance
    workflow = ReaperWorkflow(api_client)

    # Example configuration
    config = ReaperConfig(
        lot_uid="lot-12345",
        order_uid="order-67890",
        data_uid="data-abcdef",
        encoder="json",
        style="raw",
        option=ExportOption.FINISHED,
        phases=[1, 2],
        converter_uri="https://example.com/converter.py",
        exporter_name="http",
        exporter_config={
            "upload_url": "https://external-system.com/upload",
            "headers": {"Authorization": "Bearer token123"}
        }
    )

    # Run workflow
    result = workflow.run(config)
    print(f"Workflow result: {result}")


if __name__ == "__main__":
    main()
